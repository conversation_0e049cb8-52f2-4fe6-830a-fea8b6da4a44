<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test 7 - Page Break Navigation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .page {
            width: 21.59cm;
            height: 27.94cm;
            background-color: white;
            margin-bottom: 20px;
            padding: 2cm;
            box-sizing: border-box;
            position: relative;
            page-break-after: always;
            page-break-inside: avoid;
        }
        
        .toc-container {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .toc-item {
            display: grid;
            grid-template-columns: 1fr auto;
            border-bottom: 1px dotted #ccc;
            padding: 5px 0;
        }
        
        .toc-link {
            text-decoration: none;
            color: black;
            font-weight: 500;
        }
        
        .page-number {
            font-weight: bold;
            text-align: right;
        }
        
        /* Page break navigation styles */
        .page-container {
            page-break-after: always;
            page-break-inside: avoid;
            position: relative;
        }
        
        .page-anchor {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        /* Print styles */
        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
            }
            
            .page, .page-container {
                margin: 0;
                box-shadow: none;
                page-break-after: always !important;
                page-break-inside: avoid !important;
            }
            
            @page {
                margin: 0;
                size: letter;
            }
            
            .page-anchor {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                z-index: 1 !important;
            }
        }
    </style>
</head>
<body>
    <!-- TABLE OF CONTENTS PAGE -->
    <div class="page-container" id="toc-container">
        <div class="page" id="toc-page">
            <h1>Test 7: Page Break Navigation</h1>
            <p>This test uses page break properties and container-based navigation for PDF compatibility.</p>
            
            <div class="toc-container">
                <div class="toc-item">
                    <a href="#page-container-1" class="toc-link">Introduction</a>
                    <span class="page-number">1</span>
                </div>
                <div class="toc-item">
                    <a href="#page-container-2" class="toc-link">Chapter 1: Getting Started</a>
                    <span class="page-number">2</span>
                </div>
                <div class="toc-item">
                    <a href="#page-container-3" class="toc-link">Chapter 2: Advanced Topics</a>
                    <span class="page-number">3</span>
                </div>
                <div class="toc-item">
                    <a href="#page-container-4" class="toc-link">Conclusion</a>
                    <span class="page-number">4</span>
                </div>
            </div>
        </div>
    </div>

    <!-- PAGE 1 -->
    <div class="page-container" id="page-container-1">
        <div class="page-anchor" id="page-anchor-1"></div>
        <div class="page">
            <h1>Introduction</h1>
            <p>This is the introduction page. It uses page break containers for PDF navigation.</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
            <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>
    </div>

    <!-- PAGE 2 -->
    <div class="page-container" id="page-container-2">
        <div class="page-anchor" id="page-anchor-2"></div>
        <div class="page">
            <h1>Chapter 1: Getting Started</h1>
            <p>This is chapter 1 content. Testing page break container navigation.</p>
            <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
            <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt.</p>
            <p>Explicabo nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit.</p>
            <p>Sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
        </div>
    </div>

    <!-- PAGE 3 -->
    <div class="page-container" id="page-container-3">
        <div class="page-anchor" id="page-anchor-3"></div>
        <div class="page">
            <h1>Chapter 2: Advanced Topics</h1>
            <p>This is chapter 2 content. Advanced topics with page break navigation.</p>
            <p>Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.</p>
            <p>Sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.</p>
            <p>Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam.</p>
            <p>Nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate.</p>
        </div>
    </div>

    <!-- PAGE 4 -->
    <div class="page-container" id="page-container-4">
        <div class="page-anchor" id="page-anchor-4"></div>
        <div class="page">
            <h1>Conclusion</h1>
            <p>This is the conclusion page. Final thoughts with page break navigation.</p>
            <p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum.</p>
            <p>Deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.</p>
            <p>Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga.</p>
            <p>Et harum quidem rerum facilis est et expedita distinctio nam libero tempore.</p>
        </div>
    </div>
</body>
</html>
