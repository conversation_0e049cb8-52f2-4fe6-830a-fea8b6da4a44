<%
// Calculate how many pages we need
const itemsPerPage = 24;
const totalPages = Math.ceil(toc.length / itemsPerPage);

// Loop through each page
for(let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
    // Get items for this page
    const startIndex = pageIndex * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, toc.length);
    const pageItems = toc.slice(startIndex, endIndex);
%>

<div class="toc-page-container" id="toc-pageContainer-<%= pageIndex %>" style="display: flex; flex-direction: column; align-items: center; gap: 0.7cm; margin-bottom: 1cm;">
    <div class="toc-letter-container" style="width: 21.59cm; height: 27.94cm; position: relative; background-color: white; box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1); overflow: hidden;">
        <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
            class="toc-background-svg" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;">
            <g clip-path="url(#clip0_2020_2)">
                <rect width="612" height="792" fill="white" />
                <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                    <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                </g>
            </g>
            <defs>
                <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                </filter>
                <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <clipPath id="clip0_2020_2">
                    <rect width="612" height="792" fill="white" />
                </clipPath>
            </defs>
        </svg>

        <!-- Portfolio Update Content -->
        <div class="toc-portfolio-update-header" style="position: absolute; top: 3.39cm; left: 2.19cm; color: black; font-size: 0.8cm; font-family: 'Clash Display', sans-serif; font-weight: 800; word-wrap: break-word; z-index: 2;">
            Table of Contents
        </div>

        <div class="toc-container"
            style="position: absolute;
            top: 6.05cm; left: 2.19cm; right: 1.13cm; bottom: 3.2cm;
            z-index: 2; display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: repeat(12, 1fr);
            grid-auto-flow: column;
            gap: 0.7cm; grid-column-gap: 1cm;">

            <% pageItems.forEach(function(toc) { %>
                <div class="toc-item" style="display: grid; grid-template-columns: 1fr auto; align-items: center; border-bottom: 1px dotted #ccc; padding-bottom: 0.2cm;">
                    <!-- PDF bookmark destination -->
                    <a name="toc-item-<%= toc.company_name.toLowerCase().replace(/\s+/g, '-') %>" id="toc-item-<%= toc.company_name.toLowerCase().replace(/\s+/g, '-') %>"></a>

                    <!-- Simplified structure for PDF compatibility -->
                    <div style="display: grid; grid-template-columns: 1fr auto; align-items: center; width: 100%;">
                        <a href="#page-<%= toc.page_number %>"
                           style="text-decoration: none; color: black; font-family: 'Clash Display', sans-serif; font-size: 0.42cm; font-weight: 500; word-wrap: break-word;">
                            <%= toc.company_name %>
                        </a>
                        <a href="#page-<%= toc.page_number %>"
                           style="text-decoration: none; color: black; font-family: 'Clash Display', sans-serif; font-size: 0.6cm; font-weight: 700; text-align: right;">
                            <%= toc.page_number %>
                        </a>
                    </div>
                </div>
            <% }); %>
        </div>

        <div class="toc-logo-box" style="position: absolute; width: 1.62cm; height: 1.16cm; bottom: 1.06cm; right: 1.13cm; display: flex; justify-content: center; align-items: center; z-index: 2; overflow: hidden;">
            <svg width="46" height="33" viewBox="0 0 46 33" fill="none" xmlns="http://www.w3.org/2000/svg" style="max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain;">
                <path
                    d="M0.00137067 20.9346V32.9975H8.10509V19.7046H1.22966C0.550474 19.7046 0 20.2552 0 20.9346H0.00137067Z"
                    fill="#222222" />
                <path
                    d="M17.3624 7.27033C18.0416 7.27033 18.592 6.71971 18.592 6.04034V0H3.49102H1.23161C0.552425 0 0.00195312 0.550616 0.00195312 1.22998V13.097H8.10568V7.27033H17.3624Z"
                    fill="#222222" />
                <path
                    d="M27.5053 20.9346V32.9975H35.609V19.7046H28.7336C28.0544 19.7046 27.5039 20.2552 27.5039 20.9346H27.5053Z"
                    fill="#222222" />
                <path
                    d="M19.0574 13.7188C19.2751 13.334 19.6846 13.097 20.1255 13.097H27.5062V1.22998C27.5062 0.550616 28.0567 0 28.7359 0H30.8994H46.0004V6.04034C46.0004 6.71971 45.45 7.27033 44.7708 7.27033H35.6099V13.097H27.513L19.4025 27.3774H25.0236V31.77C25.0236 32.4494 24.4731 33 23.7939 33H8.10547"
                    fill="#222222" />
                <path
                    d="M35.6113 19.7059H42.2512C43.1399 19.7059 43.8602 18.9855 43.8602 18.0965V13.0972H35.6113V19.7059Z"
                    fill="#222222" />
                <path
                    d="M8.08789 19.7059H11.1675C12.0562 19.7059 12.7765 18.9855 12.7765 18.0965V13.0972H8.08789V19.7059Z"
                    fill="#222222" />
            </svg>
        </div>
    </div>
</div>
<% } %>