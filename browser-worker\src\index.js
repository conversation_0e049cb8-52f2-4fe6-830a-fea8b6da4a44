import puppeteer from "@cloudflare/puppeteer";
import { Hono } from 'hono';
import { timeout } from 'hono/timeout';

const app = new Hono();

app.get('/', (c) => {
  return c.json({ message: 'Hello, <PERSON>gar!' });
});

// Apply 2-minute timeout to the PDF route to match browser keep_alive
app.use('/pdf', timeout(120000));

// Define POST route for PDF generation
app.post('/pdf', async (c) => {
  // Get HTML content from request body
  let document;
  try {
    const contentType = c.req.header('content-type') || '';
    
    if (contentType.includes('text/html')) {
      // Handle text/html content type - entire body is HTML
      document = await c.req.text();
    } else if (contentType.includes('application/json')) {
      // Handle JSON content type
      const body = await c.req.json();
      document = body.html || "";
    } else {
      // Handle form data
      const body = await c.req.parseBody();
      
      document = body.html || "";
    }
    
    if (!document) {
      return c.json({ 
        error: "Please provide HTML content. Send as text/html content type, JSON with 'html' field, or form data with 'html' field",
        debug: {
          contentType: contentType,
          bodyLength: document ? document.length : 0
        }
      }, 400);
    }
  } catch (error) {
    console.error('Error parsing body:', error);
    return c.json({ 
      error: "Error parsing request body.",
      details: error.message 
    }, 400);
  }

//   return c.html(document);

  let pdf;
  let page;
  let browser;
  
  try {
    browser = await puppeteer.launch(c.env.BROWSER, { 
      keep_alive: 120000 // 2 minutes in milliseconds
    });
    page = await browser.newPage();
    // Step 2: Send HTML and CSS to our browser
    await page.setContent(document, { 
      waitUntil: 'networkidle0'
    });
    pdf = await page.pdf();
  } catch (error) {
    console.error('Error generating PDF:', error);
    return c.json({ 
      error: "Error generating PDF",
      details: error.message 
    }, 500);
  } finally {
    if (page) await page.close();
    if (browser) await browser.close();
  }

  // Ensure we have a valid PDF buffer
  if (!pdf || pdf.length === 0) {
    return c.json({ error: "Generated PDF is empty" }, 500);
  }

  // Return PDF with proper headers
  return c.body(pdf, 200, {
    "Content-Type": "application/pdf",
    "Content-Disposition": "attachment; filename=report.pdf",
    "Content-Length": pdf.length.toString(),
    "Cache-Control": "no-cache"
  });
});


export default {
  fetch: app.fetch,
};