<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test 1 - Basic Anchor Names</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .page {
            width: 21.59cm;
            height: 27.94cm;
            background-color: white;
            margin-bottom: 20px;
            padding: 2cm;
            box-sizing: border-box;
            position: relative;
            page-break-after: always;
        }
        
        .toc-container {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .toc-item {
            display: grid;
            grid-template-columns: 1fr auto;
            border-bottom: 1px dotted #ccc;
            padding: 5px 0;
        }
        
        .toc-link {
            text-decoration: none;
            color: black;
            font-weight: 500;
        }
        
        .page-number {
            font-weight: bold;
            text-align: right;
        }
        
        /* Print styles */
        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
            }
            
            .page {
                margin: 0;
                box-shadow: none;
                page-break-after: always;
            }
            
            @page {
                margin: 0;
                size: letter;
            }
        }
    </style>
</head>
<body>
    <!-- TABLE OF CONTENTS PAGE -->
    <div class="page" id="toc-page">
        <h1>Test 1: Basic Anchor Names</h1>
        <p>This test uses basic HTML anchor tags with name attributes and href links.</p>
        
        <div class="toc-container">
            <div class="toc-item">
                <a href="#page-1" class="toc-link">Introduction</a>
                <span class="page-number">1</span>
            </div>
            <div class="toc-item">
                <a href="#page-2" class="toc-link">Chapter 1: Getting Started</a>
                <span class="page-number">2</span>
            </div>
            <div class="toc-item">
                <a href="#page-3" class="toc-link">Chapter 2: Advanced Topics</a>
                <span class="page-number">3</span>
            </div>
            <div class="toc-item">
                <a href="#page-4" class="toc-link">Conclusion</a>
                <span class="page-number">4</span>
            </div>
        </div>
    </div>

    <!-- PAGE 1 -->
    <div class="page" id="page-1">
        <a name="page-1"></a>
        <h1>Introduction</h1>
        <p>This is the introduction page. It contains basic content to test PDF navigation.</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
    </div>

    <!-- PAGE 2 -->
    <div class="page" id="page-2">
        <a name="page-2"></a>
        <h1>Chapter 1: Getting Started</h1>
        <p>This is chapter 1 content. Testing navigation to this specific page.</p>
        <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
        <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt.</p>
        <p>Explicabo nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit.</p>
        <p>Sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
    </div>

    <!-- PAGE 3 -->
    <div class="page" id="page-3">
        <a name="page-3"></a>
        <h1>Chapter 2: Advanced Topics</h1>
        <p>This is chapter 2 content. Advanced topics are covered here.</p>
        <p>Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.</p>
        <p>Sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.</p>
        <p>Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam.</p>
        <p>Nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate.</p>
    </div>

    <!-- PAGE 4 -->
    <div class="page" id="page-4">
        <a name="page-4"></a>
        <h1>Conclusion</h1>
        <p>This is the conclusion page. Final thoughts and summary.</p>
        <p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum.</p>
        <p>Deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.</p>
        <p>Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga.</p>
        <p>Et harum quidem rerum facilis est et expedita distinctio nam libero tempore.</p>
    </div>
</body>
</html>
