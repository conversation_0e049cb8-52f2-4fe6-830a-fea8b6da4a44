// Import jsdom to provide DOMParser functionality in Node.js
const { JSDOM } = await import("npm:jsdom");
const { DOMParser } = new JSDOM().window;

const page_html = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, target-densitydpi=96, user-scalable=no">
    <meta name="pdf-navigation" content="enabled">
    <meta name="pdf-bookmarks" content="enabled">
    <title>Florida Funder Quarterly Report</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap">
    <link rel="stylesheet" href="https://api.fontshare.com/v2/css?f[]=clash-display@400,500,600&display=swap">
    <style>
        /* Base styles for the document */
        :root {
            --device-dpi: 96dpi;
            --px-per-cm: 37.7952755906; /* 96 / 2.54 (cm per inch) */
        }

        body {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            background-color: #f0f0f0;
            padding-top: 20px;
            padding-bottom: 20px;
        }

        /* Page container */
        .p6-page {
            width: 21.59cm;
            height: 27.94cm;
            position: relative;
            background-color: white;
            box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;

            page-break-after: always;
        }

        /* Common template elements */
        .p6-background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .p6-header {
            position: absolute;
            top: 3.39cm;
            left: 2.19cm;
            color: black;
            font-size: 0.53cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            z-index: 2;
        }

        .p6-logo-box {
            position: absolute;
            width: 1.62cm;
            height: 1.16cm;
            bottom: 1.06cm;
            right: 1.13cm;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
        }

        /* Container for update items */
        .p6-update-container, .update-container {
            position: absolute;
            top: 6.05cm;
            left: 2.19cm;
            right: 1.13cm;
            bottom: 3.2cm;
            z-index: 2;
            display: flex;
            flex-direction: column;
            gap: 0.1cm;

        }

        /* Styles for update items */
        .p6-update-item, .update-item {
            margin-bottom: 1cm;
        }

        .p6-company-logo-box {
            width: 4.3cm;
            height: 1.09cm;
            display: flex;
            justify-content: flex-start;
            align-items: center;

            object-fit: contain;
        }

        .p6-company-logo-box img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        .p6-fund-boxes-container {
            margin-top: 0.48cm;
            margin-bottom: 0.38cm;
            display: flex;
            gap: 0.53cm;
            justify-content: space-between;
            width: 100%;
        }

        .p6-fund-box {
            min-width: 1.5cm;
            height: 0.6cm;
            background: #B2D1B2;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Clash Display', sans-serif;
            font-size: 0.32cm;
            padding-left: 0.2cm;
            padding-right: 0.2cm;
            font-weight: 500;
            white-space: nowrap;
        }

        .p6-fund-box .p6-rank-value {
            font-weight: 700;
            margin-left: 0.14cm;
        }

        .p6-fund-boxes-left {
            display: flex;
            gap: 0.53cm;
        }

        .p6-website-text {
            color: #5CD468;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            word-break: break-word;
            overflow-wrap: break-word;
            align-self: center;
        }

        .p6-report-title {
            color: black;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            line-height: 0.64cm;
            word-break: break-word;
            overflow-wrap: break-word;
            margin-top: 0.28cm;
        }

        .p6-report-body {
            color: black;
            font-size: 0.42cm;
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            word-break: break-word;
            overflow-wrap: break-word;
            margin-top: 0.28cm;
        }

        /* Note: Continuation styling has been removed as it's no longer needed */

        /* Letter container for printing */
        .p6-letter-container {
            width: 21.59cm;
            height: 27.94cm;
        }

        /* Font declarations */
        @font-face {
            font-family: 'Montserrat';
            src: local('Montserrat'), url('https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&display=swap') format('woff');
            font-weight: 700;
            font-display: swap;
        }

        @font-face {
            font-family: 'Clash Display';
            src: local('Clash Display'), url('https://fonts.cdnfonts.com/s/65008/ClashDisplayRegular.woff') format('woff');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Clash Display';
            src: local('Clash Display'), url('https://fonts.cdnfonts.com/s/65008/ClashDisplaySemibold.woff') format('woff');
            font-weight: 600;
            font-style: normal;
            font-display: swap;
        }

        /* Print styles */
        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
                gap: 0;
                height: 27.94cm;
                width: 21.59cm;
                // overflow: hidden;
                display: block;
            }

            .p6-page, .p6-letter-container {
                margin: 0;
                box-shadow: none;
                width: 21.59cm;
                height: 27.94cm;
            }

            .p6-page-container {
                gap: 0;
            }

            @page {
                margin: 0;
                padding: 0;
                size: letter;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }

            /* Force background images and colors to print */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .p6-background-svg {
                display: block !important;
                visibility: visible !important;
            }

            /* Ensure all update items print properly */
            .p6-update-item {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            #page1-container, #page2-container, #page3-container {
                page-break-after: always;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact;
                position: relative !important;
                top: 0 !important;
                left: 0 !important;
                margin: 0 !important;
                padding: 0 !important;
                height: 27.94cm !important;
                width: 21.59cm !important;
                overflow: hidden !important;
                display: block !important;
            }

            .toc-link-container {
            cursor: pointer;
            }

            /* Fix positioning for print */
            #page1-report-container, #page1-logo-box, #company-name,
            #page2-logo-box, #page2-content, #page2-title,
            #page3-logo-box, #page3-title, #metrics-container, #additional-metrics, #cashflow-container {
                position: absolute !important;
                transform: none !important;
                float: none !important;
            }

            #company-logo, svg {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
            }

            /* PDF navigation anchor styles */
            a[name], a[id^="anchor-"] {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                visibility: hidden !important;
                display: block !important;
                height: 1px !important;
                width: 1px !important;
                z-index: 9999 !important;
            }

            /* Ensure page containers are properly positioned for navigation */
            #page-1, #page-2, [id^="page-"] {
                position: relative !important;
            }
        }

        /* General anchor styles for PDF navigation */
        a[name], a[id^="anchor-"] {
            position: absolute;
            top: 0;
            left: 0;
            visibility: hidden;
            display: block;
            height: 1px;
            width: 1px;
            z-index: 9999;
        }

        /* Ensure page containers are properly positioned for navigation */
        #page-1, #page-2, [id^="page-"] {
            position: relative;
        }
    </style>

        <script>
        // Enhanced PDF navigation support
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers for TOC items (for browsers that support JavaScript)
            const tocItems = document.querySelectorAll('.toc-link-container');
            tocItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // Let the default href behavior work first
                    // This script is mainly for enhanced behavior in browsers
                    const href = this.getAttribute('href');
                    if (href && href.startsWith('#')) {
                        const targetId = href.substring(1); // Remove the #
                        const targetElement = document.getElementById(targetId) ||
                                             document.querySelector('a[name="' + targetId + '"]') ||
                                             document.querySelector('#anchor-' + targetId);

                        if (targetElement) {
                            // For browsers that support JavaScript, add smooth scrolling
                            setTimeout(() => {
                                targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                            }, 10);
                        }
                    }
                });
            });
        });
    </script>
</head>

<body>
    <!-- PAGE 1 -->
    <div
        style="width: 21.59cm; height: 27.94cm; position: relative; background-color: white; box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1); overflow: hidden;" id="page-1">
        <!-- Anchor for PDF navigation -->
        <a name="page-1"></a>

        <!-- Background SVG -->
        <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;" id="page1-background">
            <g clip-path="url(#clip0_2008_2)">
                <rect width="612" height="792" fill="white" />
                <g opacity="0.4" filter="url(#filter0_f_2008_2)">
                    <path
                        d="M-45.1781 411.113C-49.6944 415.201 -56.0779 416.523 -61.8466 414.611L-158.41 382.604C-158.499 382.574 -158.41 382.604 -158.499 382.574L9.49567 231.05L-64.0465 206.674L-45.0115 149.247C-42.0676 140.365 -32.4795 135.554 -23.5936 138.499L181.661 206.534"
                        fill="url(#paint0_linear_2008_2)" />
                </g>
                <rect opacity="0.4" x="614.354" y="595.621" width="72.0003" height="362.138"
                    transform="rotate(-150 614.354 595.621)" fill="url(#paint1_linear_2008_2)" />
                <g opacity="0.5" filter="url(#filter1_f_2008_2)">
                    <ellipse cx="623" cy="486.499" rx="94" ry="93.5" fill="url(#paint2_linear_2008_2)" />
                </g>
                <g opacity="0.5" filter="url(#filter2_f_2008_2)">
                    <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint3_linear_2008_2)" />
                </g>
            </g>
            <defs>
                <filter id="filter0_f_2008_2" x="-222.499" y="73.6331" width="468.161" height="405.838"
                    filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="32" result="effect1_foregroundBlur_2008_2" />
                </filter>
                <filter id="filter1_f_2008_2" x="329" y="192.999" width="588" height="587" filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2008_2" />
                </filter>
                <filter id="filter2_f_2008_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2008_2" />
                </filter>
                <linearGradient id="paint0_linear_2008_2" x1="273.858" y1="154.945" x2="-484.695" y2="392.721"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <linearGradient id="paint1_linear_2008_2" x1="587.2" y1="991.104" x2="845.275" y2="953.261"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <linearGradient id="paint2_linear_2008_2" x1="62.732" y1="735.245" x2="747.74" y2="-77.0686"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <linearGradient id="paint3_linear_2008_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <clipPath id="clip0_2008_2">
                    <rect width="612" height="792" fill="white" />
                </clipPath>
            </defs>
        </svg>

        <!-- Report Container -->
        <div
            style="position: absolute; width: 100%; height: 6.38cm; left: 2.26cm; bottom: calc(3.62cm + 2.12cm); display: flex; flex-direction: column; justify-content: flex-start; z-index: 2;" id="page1-report-container">
            <div
                style="color: black; font-size: 1.09cm; font-weight: 600; font-family: 'Clash Display', sans-serif; margin-bottom: 0; line-height: 1.2;" id="report-title">
                Q3 2023 Investor Report</div>
            <div
                style="color: black; font-size: 0.85cm; font-weight: 600; font-family: 'Clash Display', sans-serif; margin: 1rem 0; line-height: 1;">
                &nbsp;</div>
            <div style="font-family: 'Clash Display', sans-serif; color: black; line-height: 1.5;" id="report-details">
                <span style="font-size: 0.42cm; font-weight: 600;">Fund Name: </span>
                <span style="font-size: 0.42cm; font-weight: 400;" id="fund-name">Growth Equity Fund II</span>
                <br>
                <span style="font-size: 0.42cm; font-weight: 600;">Reporting Period: </span>
                <span style="font-size: 0.42cm; font-weight: 400;" id="reporting-period">Q3 2023</span>
            </div>
        </div>

        <!-- Logo Box -->
        <div
            style="position: absolute; width: 2.82cm; height: 2.12cm; bottom: 3.62cm; left: 2.61cm; display: flex; justify-content: center; align-items: center; z-index: 2;" id="page1-logo-box">
            <img style="width: 100%; height: 100%; object-fit: contain; max-width: 100%; max-height: 100%;"
                src="https://storage.googleapis.com/x8xh-wdgz-rmil.n7e.xano.io/vault/gQxBdSlG/46fp2_mWEeQzS8H27JjH58q90tI/2SMsUg../rendyr-logo-colored.svg"
                alt="Company Logo" id="company-logo">
        </div>

        <!-- Company Name -->
        <div
            style="position: absolute; left: calc(2.61cm + 2.82cm + 0.56cm); bottom: calc(3.62cm + (2.12cm / 2) - (0.52cm / 2)); display: flex; align-items: center; z-index: 2; font-family: 'Gotham Black', 'Montserrat', sans-serif; font-size: 0.52cm; line-height: 1; font-weight: 700; text-transform: uppercase; letter-spacing: 0.12cm; word-spacing: 0cm; color: #000;" id="company-name">
            RENDYR</div>
    </div>

    <!-- PAGE 2 -->
    <div
        style="width: 21.59cm; height: 27.94cm; position: relative; background-color: white; box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1); overflow: hidden;" id="page-2">
        <!-- Anchor for PDF navigation -->
        <a name="page-2"></a>

        <!-- Background SVG -->
        <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
            style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;" id="page2-background">
            <g clip-path="url(#clip0_2020_2)">
                <rect width="612" height="792" fill="white" />
                <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                    <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                </g>
            </g>
            <defs>
                <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                    color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                    <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                </filter>
                <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color="#D1F15E" />
                    <stop offset="0.94" stop-color="#70E9EF" />
                </linearGradient>
                <clipPath id="clip0_2020_2">
                    <rect width="612" height="792" fill="white" />
                </clipPath>
            </defs>
        </svg>

        <!-- Logo Box -->
        <div
            style="position: absolute; width: 1.62cm; height: 1.16cm; bottom: 1.06cm; right: 1.13cm; display: flex; justify-content: center; align-items: center; z-index: 2; overflow: hidden;" id="page2-logo-box">
            <svg width="46" height="33" viewBox="0 0 46 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M0.00137067 20.9346V32.9975H8.10509V19.7046H1.22966C0.550474 19.7046 0 20.2552 0 20.9346H0.00137067Z"
                    fill="#222222" />
                <path
                    d="M17.3624 7.27033C18.0416 7.27033 18.592 6.71971 18.592 6.04034V0H3.49102H1.23161C0.552425 0 0.00195312 0.550616 0.00195312 1.22998V13.097H8.10568V7.27033H17.3624Z"
                    fill="#222222" />
                <path
                    d="M27.5053 20.9346V32.9975H35.609V19.7046H28.7336C28.0544 19.7046 27.5039 20.2552 27.5039 20.9346H27.5053Z"
                    fill="#222222" />
                <path
                    d="M19.0574 13.7188C19.2751 13.334 19.6846 13.097 20.1255 13.097H27.5062V1.22998C27.5062 0.550616 28.0567 0 28.7359 0H30.8994H46.0004V6.04034C46.0004 6.71971 45.45 7.27033 44.7708 7.27033H35.6099V13.097H27.513L19.4025 27.3774H25.0236V31.77C25.0236 32.4494 24.4731 33 23.7939 33H8.10547"
                    fill="#222222" />
                <path
                    d="M35.6113 19.7059H42.2512C43.1399 19.7059 43.8602 18.9855 43.8602 18.0965V13.0972H35.6113V19.7059Z"
                    fill="#222222" />
                <path
                    d="M8.08789 19.7059H11.1675C12.0562 19.7059 12.7765 18.9855 12.7765 18.0965V13.0972H8.08789V19.7059Z"
                    fill="#222222" />
            </svg>
        </div>

        <!-- Text Box -->
        <div
            style="position: absolute; top: 3.39cm; left: 2.19cm; bottom: 3.39cm; width: 16.90cm; overflow: hidden; font-family: 'Clash Display', sans-serif; z-index: 1;" id="page2-content">
            <div style="width: 100%; height: 1.45cm; display: flex; align-items: center;">
                <div
                    style="color: black; font-size: 0.85cm; font-family: 'Clash Display', sans-serif; font-weight: 600; word-wrap: break-word; width: 100%;" id="executive-summary-title">
                    Executive Summary</div>
            </div>
            <div id="exec-paragraph">

            </div>
        </div>
    </div>


    <!-- Table of Contents Page PLACEHOLDER -->

    <div id="pages-container"></div>
<!-- Template for individual pages - will be cloned by JavaScript -->
    <template id="page-template">
        <div class="p6-page">
            <!-- Background SVG -->
            <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
                class="p6-background-svg">
                <g clip-path="url(#clip0_2020_2)">
                    <rect width="612" height="792" fill="white" />
                    <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                        <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                    </g>
                </g>
                <defs>
                    <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                        <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                    </filter>
                    <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#D1F15E" />
                        <stop offset="0.94" stop-color="#70E9EF" />
                    </linearGradient>
                    <clipPath id="clip0_2020_2">
                        <rect width="612" height="792" fill="white" />
                    </clipPath>
                </defs>
            </svg>

            <!-- Fixed header element -->
            <div class="p6-header">Portfolio Company Updates</div>

            <!-- Container for update items - will be filled by JavaScript -->
            <div class="p6-update-container"></div>

            <!-- Fixed logo element -->
            <div class="p6-logo-box">
                <svg width="46" height="33" viewBox="0 0 46 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.00137067 20.9346V32.9975H8.10509V19.7046H1.22966C0.550474 19.7046 0 20.2552 0 20.9346H0.00137067Z" fill="#222222"/>
                    <path d="M17.3624 7.27033C18.0416 7.27033 18.592 6.71971 18.592 6.04034V0H3.49102H1.23161C0.552425 0 0.00195312 0.550616 0.00195312 1.22998V13.097H8.10568V7.27033H17.3624Z" fill="#222222"/>
                    <path d="M27.5053 20.9346V32.9975H35.609V19.7046H28.7336C28.0544 19.7046 27.5039 20.2552 27.5039 20.9346H27.5053Z" fill="#222222"/>
                    <path d="M19.0574 13.7188C19.2751 13.334 19.6846 13.097 20.1255 13.097H27.5062V1.22998C27.5062 0.550616 28.0567 0 28.7359 0H30.8994H46.0004V6.04034C46.0004 6.71971 45.45 7.27033 44.7708 7.27033H35.6099V13.097H27.513L19.4025 27.3774H25.0236V31.77C25.0236 32.4494 24.4731 33 23.7939 33H8.10547" fill="#222222"/>
                    <path d="M35.6113 19.7059H42.2512C43.1399 19.7059 43.8602 18.9855 43.8602 18.0965V13.0972H35.6113V19.7059Z" fill="#222222"/>
                    <path d="M8.08789 19.7059H11.1675C12.0562 19.7059 12.7765 18.9855 12.7765 18.0965V13.0972H8.08789V19.7059Z" fill="#222222"/>
                </svg>
            </div>
        </div>
    </template>

    <!-- PAGE 7 PLACEHOLDER -->

    <!-- PAGE 8 PLACEHOLDER -->


</body>

</html>`

/**
 * Portfolio Updates Template System
 *
 * This script handles the pagination and generation of company update pages,
 * ensuring proper distribution of content across multiple pages with identical structure.
 */

// Debug configuration
const DEBUG = {
    enabled: false,  // Always enable logging
    visualizeSplits: false,  // No visual splits
    logMeasurements: false,  // No measurement logs
    showGrids: false  // No measurement grids
};

// Create a DOM parser to work with the page_html content
const parser = new DOMParser();
let htmlDoc = parser.parseFromString(page_html, 'text/html');

/**
 * Enable debug mode with visual indicators - No longer needed but kept for reference
 */
function enableDebugMode() {
    // Function is now empty - kept for compatibility only
    console.log('Standard logging is enabled by default');
}

/**
 * Initializes the portfolio template system with the provided data
 * @param {Object} data - The portfolio company data
 * @returns {Object} Object containing the final HTML and table of contents data
 */
function initializePortfolioTemplate(data) {
    const pagesContainer = htmlDoc.getElementById('pages-container');
    const companies = data.companies || [];

    // Initialize table of contents array to track company names and page numbers
    const tocItems = [];

    // Clear any existing content
    pagesContainer.innerHTML = '';

    // Start page counter at 3 (after cover page and executive summary)
    let currentPageNumber = 7;

    // Helper function to create a new page and set it up
    function createAndSetupPage(pageNum) {
        const newPage = createNewPage(pageNum);
        pagesContainer.appendChild(newPage);
        return {
            page: newPage,
            container: newPage.querySelector('.p6-update-container')
        };
    }

    // Helper function to update the TOC entry for the current company
    function updateTocEntry(tocEntry, newPageNum) {
        tocEntry.page_number = newPageNum;
        if (DEBUG.enabled) {
            console.log(`Updated TOC entry for ${tocEntry.company_name} to page ${newPageNum}`);
        }
    }

    // Helper function to add an item to a page and update remaining height
    function addItemToPage(item, container, itemHeight) {
        container.appendChild(item);
        const newRemainingHeight = remainingHeight - itemHeight;
        if (DEBUG.enabled) {
            console.log(`Added item to page. Remaining height: ${newRemainingHeight.toFixed(2)}cm`);
        }
        return newRemainingHeight;
    }

    // Create the first page and initialize variables
    let currentPage, currentContainer;
    ({ page: currentPage, container: currentContainer } = createAndSetupPage(currentPageNumber));



    // Calculate available height for content
    const maxContainerHeight = $var.maxContainerHeight
    let remainingHeight = maxContainerHeight;

    // Sort companies alphabetically by name
    companies.sort((a, b) => a.name.localeCompare(b.name));

    // Process each company and distribute across pages
    companies.forEach((company, index) => {
        if (DEBUG.enabled) {
            console.log(`Processing company ${index + 1}/${companies.length}: ${company.name}`);
            console.log(`Remaining height: ${remainingHeight.toFixed(2)}cm`);
        }

        // Special handling for companies with add == true property
        if (company.add === true) {
            if (DEBUG.enabled) {
                console.log(`Company ${company.name} has add == true. Checking for special pagination rules.`);
            }

            // Check if the next company also has add == true (consecutive flagged companies)
            const nextCompany = companies[index + 1];
            const isNextCompanyFlagged = nextCompany && nextCompany.add === true;

            if (isNextCompanyFlagged) {
                if (DEBUG.enabled) {
                    console.log(`Next company ${nextCompany.name} also has add == true. Two consecutive flagged companies detected.`);
                }

                // Check if there's a company after the next one
                const companyAfterNext = companies[index + 2];
                if (companyAfterNext) {
                    if (DEBUG.enabled) {
                        console.log(`Company after next sequence exists: ${companyAfterNext.name}. Will force it to a new page.`);
                    }
                    
                    // Mark that we need to force a page break after processing the next company
                    // We'll use a flag that gets checked when processing the next company
                    nextCompany._forcePageBreakAfter = true;
                }
            } else {
                // Single company with add == true - force next company to same page
                if (nextCompany) {
                    if (DEBUG.enabled) {
                        console.log(`Single flagged company. Will force next company ${nextCompany.name} to same page, bypassing height checks.`);
                    }
                    
                    // Mark the next company to bypass height checks
                    nextCompany._bypassHeightCheck = true;
                }
            }
        }

        // Check if this company was marked to force a page break after processing
        const shouldForcePageBreakAfter = company._forcePageBreakAfter === true;

        // Check if this company should bypass height checks (forced to same page by previous company)
        const shouldBypassHeightCheck = company._bypassHeightCheck === true;

        if (shouldBypassHeightCheck) {
            if (DEBUG.enabled) {
                console.log(`Company ${company.name} is bypassing height checks - forced to same page by previous flagged company.`);
            }
        }

        // Create full update item
        const fullUpdateItem = createUpdateItemElement(company);

        // Measure the item height with a small buffer for accuracy (5%)
        // Calculate height once and convert as needed to avoid redundant calculations
        const rawHeightPx = calculateElementHeight(fullUpdateItem, true);
        const fullItemHeightPx = rawHeightPx * 1.05;
        const fullItemHeight = fullItemHeightPx / 37.7952755906; // Convert to cm

        if (DEBUG.enabled) {
            console.log(`Full item height: ${fullItemHeight.toFixed(2)}cm (${Math.round(fullItemHeightPx)}px)`);
        }

        // Add this company to the table of contents with the current page number
        tocItems.push({
            company_name: company.name,
            page_number: currentPageNumber
        });

        // Apply special pagination rules if applicable
        if (shouldBypassHeightCheck) {
            // Force this company to the current page, bypassing all height checks
            remainingHeight = addItemToPage(fullUpdateItem, currentContainer, fullItemHeight);
            
            if (DEBUG.enabled) {
                console.log(`Forced company ${company.name} to current page. Remaining height: ${remainingHeight.toFixed(2)}cm`);
            }
        } else {
            // Use existing height-based pagination logic
            // Check if we need to start a new page
            // If remaining height is very small (less than 1cm), start a new page anyway
            if (remainingHeight < 1.0) {
                if (DEBUG.enabled) {
                    console.log(`Remaining space too small (${remainingHeight.toFixed(2)}cm). Creating new page.`);
                }

                // Increment page number for the new page
                currentPageNumber++;

                // Create a new page using our helper function
                const newPageData = createAndSetupPage(currentPageNumber);
                currentPage = newPageData.page;
                currentContainer = newPageData.container;
                remainingHeight = maxContainerHeight;

                // Update the page number for this company in the TOC
                updateTocEntry(tocItems[tocItems.length - 1], currentPageNumber);
            }

            // Check if entire item fits on current page
            if (fullItemHeight <= remainingHeight) {
                // Item fits entirely - add it to the page
                remainingHeight = addItemToPage(fullUpdateItem, currentContainer, fullItemHeight);

                // Add debug visualization
                if (DEBUG.enabled) {
                    console.log(`Item fits entirely. Remaining height: ${remainingHeight.toFixed(2)}cm`);
                }
            } else {
                // Item doesn't fit entirely - move to a new page instead of splitting
                if (DEBUG.enabled) {
                    console.log(`Item does not fit entirely. Moving to a new page...`);
                }

                // Increment page number for the new page
                currentPageNumber++;

                // Create a new page for this company using our helper function
                const newPageData = createAndSetupPage(currentPageNumber);
                currentPage = newPageData.page;
                currentContainer = newPageData.container;
                remainingHeight = maxContainerHeight;

                // Update the page number for this company in the TOC
                updateTocEntry(tocItems[tocItems.length - 1], currentPageNumber);

                // Check if the item fits on the new page
                if (fullItemHeight <= remainingHeight) {
                    // Item fits on the new page
                    remainingHeight = addItemToPage(fullUpdateItem, currentContainer, fullItemHeight);

                    if (DEBUG.enabled) {
                        console.log(`Item fits on new page. Remaining height: ${remainingHeight.toFixed(2)}cm`);
                    }
                } else {
                    // Item is too large for a single page
                    // This is an edge case - we'll still add it to the page and let it overflow
                    // rather than splitting it across pages
                    if (DEBUG.enabled) {
                        console.log(`Warning: Company section for ${company.name} is too large to fit on a single page.`);
                    }

                    // Try to reduce the content if possible
                    const compactItem = tryCompactContent(fullUpdateItem);
                    // Apply the same buffer for consistency with fullItemHeight
                    const compactHeight = calculateElementHeight(compactItem) * 1.05;

                    if (compactHeight <= remainingHeight) {
                        // Compacted item fits
                        remainingHeight = addItemToPage(compactItem, currentContainer, compactHeight);

                        if (DEBUG.enabled) {
                            console.log(`Compacted item fits. Remaining height: ${remainingHeight.toFixed(2)}cm`);
                        }
                    } else {
                        // Even compacted item doesn't fit - add it anyway and mark page as full
                        currentContainer.appendChild(fullUpdateItem);
                        remainingHeight = 0; // Mark the page as full explicitly

                        // Increment page number for the next company
                        currentPageNumber++;
                        // Create a new page for the next company using our helper function
                        const newPageData = createAndSetupPage(currentPageNumber);
                        currentPage = newPageData.page;
                        currentContainer = newPageData.container;
                        remainingHeight = maxContainerHeight;

                        if (DEBUG.enabled) {
                            console.log(`Created a new page after oversized item. Remaining height: ${remainingHeight.toFixed(2)}cm`);
                        }
                    }
                }
            }
        }

        // Handle forced page break after processing (for consecutive flagged companies)
        if (shouldForcePageBreakAfter) {
            if (DEBUG.enabled) {
                console.log(`Forcing page break after ${company.name} due to consecutive flagged companies rule.`);
            }

            // Increment page number for the next company
            currentPageNumber++;

            // Create a new page for the next company using our helper function
            const newPageData = createAndSetupPage(currentPageNumber);
            currentPage = newPageData.page;
            currentContainer = newPageData.container;
            remainingHeight = maxContainerHeight;

            if (DEBUG.enabled) {
                console.log(`Created new page after consecutive flagged companies. Page number: ${currentPageNumber}`);
            }
        }
    });

    // Log pagination info
    // const totalPages = pagesContainer.querySelectorAll('.p6-page').length;
    // console.log(`Created ${totalPages} pages for ${companies.length} companies`);

    // const adjustDriftingToCPages = Math.ceil( tocItems.length / 24 );
    // tocItems.forEach((item) => {
    //     item.page_number += adjustDriftingToCPages;
    // });

    return {
        html: htmlDoc.documentElement.outerHTML,
        toc: tocItems
    };
}

/**
 * Creates a new page from the template
 * @param {number} pageNumber - The page number for creating unique ID
 * @returns {HTMLElement} The new page element
 */
function createNewPage(pageNumber = null) {
    const pageTemplate = htmlDoc.getElementById('page-template');

    const newPage = pageTemplate.content.cloneNode(true).firstElementChild;

    if (pageNumber !== null) {
        // Create a destination ID that matches what's used in the PDF bookmarks
        const destId = `page-${pageNumber}`;
        newPage.id = destId;

        // Create an anchor that PDF generators can use as a destination
        const anchor = htmlDoc.createElement('a');
        // Use name attribute for PDF compatibility (preferred by most PDF readers)
        anchor.setAttribute('name', destId);
        anchor.setAttribute('data-pdf-dest', destId);
        anchor.setAttribute('data-pdf-bookmark', `Page ${pageNumber}`);
        // Make anchor visible but tiny for better PDF compatibility
        anchor.style.position = 'absolute';
        anchor.style.top = '0';
        anchor.style.left = '0';
        anchor.style.display = 'block';
        anchor.style.height = '1px';
        anchor.style.width = '1px';
        anchor.style.fontSize = '1px';
        anchor.style.lineHeight = '1px';
        anchor.style.color = 'transparent';
        anchor.style.zIndex = '9999';

        newPage.insertBefore(anchor, newPage.firstChild);

        // Create and add page number element
        const pageNumberElement = htmlDoc.createElement('div');
        pageNumberElement.className = 'p6-page-number';
        pageNumberElement.style.position = 'absolute';
        pageNumberElement.style.bottom = '0.7cm';
        pageNumberElement.style.left = '1.7cm';
        pageNumberElement.style.color = 'black';
        pageNumberElement.style.fontSize = '0.56cm';
        pageNumberElement.style.fontFamily = "'Clash Display', sans-serif";
        pageNumberElement.style.fontWeight = '700';
        pageNumberElement.style.lineHeight = '0.85cm';
        pageNumberElement.style.wordWrap = 'break-word';
        pageNumberElement.style.zIndex = '2';
        pageNumberElement.textContent = pageNumber.toString();

        newPage.appendChild(pageNumberElement);
    }

    return newPage;
}


/**
 * Calculates the height an element would have when rendered
 * @param {HTMLElement} element - The element to measure
 * @param {boolean} returnRawPx - If true, returns the raw pixel value instead of converted cm
 * @returns {number} The element's height in cm (or px if returnRawPx is true)
 */
function calculateElementHeight(element, returnRawPx = false) {
    // Create a temporary container for measurement with the correct width constraints
    const tempContainer = htmlDoc.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.visibility = 'hidden';

    // Use exact container dimensions from CSS - left: 2.19cm, right: 1.13cm from a 21.59cm page width
    const pageWidth = 21.59; // Letter width in cm
    const leftMargin = 2.19; // Left margin in cm
    const rightMargin = 1.13; // Right margin in cm
    const contentWidth = pageWidth - leftMargin - rightMargin; // Actual content width
    tempContainer.style.width = contentWidth + 'cm';

    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';

    // Clone the element to avoid modifying the original
    const clone = element.cloneNode(true);
    tempContainer.appendChild(clone);
    htmlDoc.body.appendChild(tempContainer);

    // Get the conversion factor for pixels to cm
    const pxPerCm = 37.7952755906; // Default: 96/2.54

    // Enhanced measurement approach for JSDOM environment
    // This uses a more sophisticated algorithm that considers element types and content structure
    let totalHeightPx = 0;

    // Process the element based on its type and content
    function processElement(el) {
        // Get element type
        const tagName = el.tagName?.toLowerCase();

        // Get computed or inline styles where possible
        const fontSize = parseFloat(el.style.fontSize) || getDefaultFontSize(tagName);
        const lineHeight = parseFloat(el.style.lineHeight) || fontSize * 1.2; // Default line-height multiplier

        // Get content and calculate word-wrapped lines
        const text = el.textContent || '';
        const words = text.split(/\s+/);

        // Calculate lines based on word wrapping
        let currentLine = '';
        let lineCount = 1; // Start with at least one line

        // Average character width based on font size (approximation)
        const avgCharWidth = fontSize * 0.5; // Approximate character width
        const maxLineWidth = contentWidth * pxPerCm; // Max line width in pixels

        // Process words to calculate line wrapping
        words.forEach(word => {
            const wordWidth = word.length * avgCharWidth;
            const currentLineWidth = currentLine.length * avgCharWidth;

            if (currentLine && (currentLineWidth + wordWidth + avgCharWidth > maxLineWidth)) {
                // Word doesn't fit on current line, start a new line
                lineCount++;
                currentLine = word;
            } else {
                // Word fits on current line
                currentLine += (currentLine ? ' ' : '') + word;
            }
        });

        // Calculate height based on lines and line height
        const contentHeight = lineCount * lineHeight;

        // Add margins and padding
        const marginTop = parseFloat(el.style.marginTop) || getDefaultMargin(tagName, 'top');
        const marginBottom = parseFloat(el.style.marginBottom) || getDefaultMargin(tagName, 'bottom');
        const paddingTop = parseFloat(el.style.paddingTop) || getDefaultPadding(tagName, 'top');
        const paddingBottom = parseFloat(el.style.paddingBottom) || getDefaultPadding(tagName, 'bottom');

        // Calculate total element height
        return contentHeight + marginTop + marginBottom + paddingTop + paddingBottom;
    }

    // Helper function to get default font size based on element type
    function getDefaultFontSize(tagName) {
        switch (tagName) {
            case 'h1': return 32;
            case 'h2': return 24;
            case 'h3': return 18.72;
            case 'h4': return 16;
            case 'h5': return 13.28;
            case 'h6': return 10.72;
            default: return 16; // Default for paragraphs and other elements
        }
    }

    // Helper function to get default margins
    function getDefaultMargin(tagName, side) {
        if (tagName === 'p' && (side === 'top' || side === 'bottom')) {
            return 16; // Default paragraph margins
        }
        if (/^h[1-6]$/.test(tagName)) {
            return side === 'top' ? 21 : 13; // Default heading margins
        }
        return 0;
    }

    // Helper function to get default padding
    function getDefaultPadding() {
        // Most elements don't have default padding
        return 0;
    }

    // Process the main element
    totalHeightPx = processElement(clone);

    // Process child elements that might affect layout
    let childrenHeight = 0;

    // Only consider direct children that are block elements
    Array.from(clone.children).forEach(child => {
        const display = child.style.display || getDefaultDisplay(child.tagName?.toLowerCase());
        if (display === 'block') {
            childrenHeight += processElement(child);
        }
    });

    // Helper function to get default display value
    function getDefaultDisplay(tagName) {
        const blockElements = ['div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'table', 'form', 'header', 'footer', 'section', 'article'];
        return blockElements.includes(tagName) ? 'block' : 'inline';
    }

    // Use the larger of the two calculations (direct or sum of children)
    // This helps handle both simple and complex nested layouts
    totalHeightPx = Math.max(totalHeightPx, childrenHeight);

    // Add a small buffer for complex layouts (10%)
    totalHeightPx *= 1.1;

    // Convert to cm if needed
    const totalHeightCm = totalHeightPx / pxPerCm;

    // Remove the temporary container
    htmlDoc.body.removeChild(tempContainer);

    if (DEBUG.enabled && DEBUG.logMeasurements) {
        console.log(`Element height estimate: ${totalHeightCm.toFixed(2)}cm (${Math.round(totalHeightPx)}px) using enhanced algorithm`);
    }

    // Return the appropriate measurement
    return returnRawPx ? totalHeightPx : totalHeightCm;
}

/**
 * Creates a complete update item element from company data
 * @param {Object} company - The company data
 * @returns {HTMLElement} The update item element
 */
function createUpdateItemElement(company) {
    const updateItem = htmlDoc.createElement('div');
    updateItem.className = 'p6-update-item';
    updateItem.setAttribute('data-company-name', company.name);

    // Enhanced PDF navigation - add multiple anchor types for company
    const companyAnchor = htmlDoc.createElement('a');
    const companyId = company.name.toLowerCase().replace(/\s+/g, '-');
    // Use id instead of name attribute (name is deprecated)
    companyAnchor.id = `anchor-company-${companyId}`;
    companyAnchor.style.position = 'absolute';
    companyAnchor.style.top = '0';
    companyAnchor.style.left = '0';
    companyAnchor.style.visibility = 'hidden';

    // Add a destination attribute for PDF compatibility
    companyAnchor.setAttribute('data-pdf-dest', `company-${companyId}`);

    // HTML structure for the update item
    updateItem.innerHTML = `
        <div class="p6-company-logo-box">
            <img src="${company.logo}" alt="${company.name} Logo">
        </div>
        <div class="p6-fund-boxes-container">
            <div class="p6-fund-boxes-left">
                <div class="p6-fund-box">${company.fund}</div>
                <div class="p6-fund-box">Rank: <span class="p6-rank-value">${company.rank}</span></div>
            </div>
            <div class="p6-website-text">${company.website}</div>
        </div>
        <div class="p6-report-container">
            <div class="p6-report-title">
                ${company.name}
            </div>
            <div class="p6-report-body">
                ${company.report_body}
            </div>
        </div>
    `;

    // Insert the anchor at the beginning of the update item
    updateItem.insertBefore(companyAnchor, updateItem.firstChild);

    return updateItem;
}

/**
 * Creates just the header part of a company update (logo, fund, title)
 * @param {Object} company - The company data
 * @returns {HTMLElement} The header element
 */
function createCompanyHeaderElement(company) {
    const headerItem = htmlDoc.createElement('div');
    headerItem.className = 'p6-update-item';
    headerItem.setAttribute('data-company-name', company.name);

    // HTML structure for just the header parts
    headerItem.innerHTML = `
        <div class="p6-company-logo-box">
            <img src="${company.logo}" alt="${company.name} Logo">
        </div>
        <div class="p6-fund-boxes-container">
            <div class="p6-fund-boxes-left">
                <div class="p6-fund-box">${company.fund}</div>
                <div class="p6-fund-box">Rank: <span class="p6-rank-value">${company.rank}</span></div>
            </div>
            <div class="p6-website-text">${company.website}</div>
        </div>
        <div class="p6-report-container">
            <div class="p6-report-title">
                ${company.name}
            </div>
            <div class="p6-report-body">
            </div>
        </div>
    `;

    return headerItem;
}

// Note: The functions createContinuationElement, createPartialHeaderElement, and splitReportBodyText
// have been removed as they are no longer needed with the simplified pagination approach
// that keeps company sections together on the same page.

/**
 * Attempts to create a more compact version of an update item by reducing content
 * @param {HTMLElement} updateItem - The original update item
 * @returns {HTMLElement} A compacted version of the update item
 */
function tryCompactContent(updateItem) {
    // Clone the update item to avoid modifying the original
    const compactItem = updateItem.cloneNode(true);

    // Find the report body element
    const reportBody = compactItem.querySelector('.p6-report-body');
    if (!reportBody) return compactItem; // If no report body, return as is

    // Get the current text content
    const originalText = reportBody.textContent || '';

    // If text is already short, don't modify
    if (originalText.length < 300) return compactItem;

    // Truncate the text to a reasonable length and add ellipsis
    // Try to find a sentence end for cleaner truncation
    let truncatedText = originalText.substring(0, 300);
    const lastPeriodIndex = truncatedText.lastIndexOf('.');

    if (lastPeriodIndex > 200) {
        // If we found a period in a reasonable position, truncate there
        truncatedText = truncatedText.substring(0, lastPeriodIndex + 1);
    } else {
        // Otherwise, try to find the last space to avoid cutting words
        const lastSpaceIndex = truncatedText.lastIndexOf(' ');
        if (lastSpaceIndex > 0) {
            truncatedText = truncatedText.substring(0, lastSpaceIndex);
        }
        truncatedText += '...';
    }

    // Update the report body with the truncated text
    reportBody.textContent = truncatedText;

    return compactItem;
}


function updatePages(data) {
    // Update Page 1
    htmlDoc.getElementById('report-title').textContent = data.report_title;
    htmlDoc.getElementById('fund-name').textContent = data.fund_name;
    htmlDoc.getElementById('reporting-period').textContent = data.reporting_period;
    htmlDoc.getElementById('company-name').textContent = data.company_name;

    // Update Page 2
    htmlDoc.getElementById('executive-summary-title').textContent = data.executive_summary_title;

    // Update paragraphs
    const execParagraphContainer = htmlDoc.getElementById('exec-paragraph');
    if (execParagraphContainer && data.paragraphs && data.paragraphs.length > 0) {
        // Clear existing content
        execParagraphContainer.innerHTML = '';

        // Loop through paragraphs and create a div for each
        data.paragraphs.forEach((paragraph, index) => {
            const paragraphDiv = htmlDoc.createElement('div');
            paragraphDiv.style.marginTop = '0.81cm';
            paragraphDiv.id = `exec-paragraph-${index + 1}`;

            const paragraphElement = htmlDoc.createElement('p');
            paragraphElement.style.color = 'black';
            paragraphElement.style.fontSize = '0.42cm';
            paragraphElement.style.fontFamily = "'Clash Display', sans-serif";
            paragraphElement.style.fontWeight = '400';
            paragraphElement.style.lineHeight = '0.63cm';
            paragraphElement.style.wordBreak = 'break-word';
            paragraphElement.style.overflowWrap = 'break-word';
            paragraphElement.textContent = paragraph;

            paragraphDiv.appendChild(paragraphElement);
            execParagraphContainer.appendChild(paragraphDiv);
        });
    }

    return htmlDoc.documentElement.outerHTML;
}

const data = $var.data

// Call functions and get the updated HTML
const updatedHtml = updatePages(data);
const result = initializePortfolioTemplate(data);

// Return both the final HTML and the table of contents data
return {
    html: result.html,
    toc: result.toc
};