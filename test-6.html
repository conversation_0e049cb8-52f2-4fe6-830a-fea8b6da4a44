<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test 6 - Multiple Anchor Techniques Combined</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        
        .page {
            width: 21.59cm;
            height: 27.94cm;
            background-color: white;
            margin-bottom: 20px;
            padding: 2cm;
            box-sizing: border-box;
            position: relative;
            page-break-after: always;
        }
        
        .toc-container {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .toc-item {
            display: grid;
            grid-template-columns: 1fr auto;
            border-bottom: 1px dotted #ccc;
            padding: 5px 0;
        }
        
        .toc-link {
            text-decoration: none;
            color: black;
            font-weight: 500;
        }
        
        .page-number {
            font-weight: bold;
            text-align: right;
        }
        
        /* Multiple anchor styles */
        .anchor-target {
            position: absolute;
            top: 0;
            left: 0;
            visibility: hidden;
            display: block;
            height: 1px;
            width: 1px;
            z-index: 9999;
        }
        
        /* Print styles */
        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
            }
            
            .page {
                margin: 0;
                box-shadow: none;
                page-break-after: always;
            }
            
            @page {
                margin: 0;
                size: letter;
            }
            
            .anchor-target {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                visibility: hidden !important;
                display: block !important;
                height: 1px !important;
                width: 1px !important;
                z-index: 9999 !important;
            }
        }
    </style>
</head>
<body>
    <!-- TABLE OF CONTENTS PAGE -->
    <div class="page" id="toc-page">
        <h1>Test 6: Multiple Anchor Techniques Combined</h1>
        <p>This test combines multiple anchor techniques: name attributes, IDs, and hidden elements.</p>
        
        <div class="toc-container">
            <div class="toc-item">
                <a href="#multi-page1" class="toc-link">Introduction</a>
                <span class="page-number">1</span>
            </div>
            <div class="toc-item">
                <a href="#multi-page2" class="toc-link">Chapter 1: Getting Started</a>
                <span class="page-number">2</span>
            </div>
            <div class="toc-item">
                <a href="#multi-page3" class="toc-link">Chapter 2: Advanced Topics</a>
                <span class="page-number">3</span>
            </div>
            <div class="toc-item">
                <a href="#multi-page4" class="toc-link">Conclusion</a>
                <span class="page-number">4</span>
            </div>
        </div>
    </div>

    <!-- PAGE 1 -->
    <div class="page" id="multi-page1">
        <!-- Multiple anchor techniques for maximum compatibility -->
        <a name="multi-page1"></a>
        <a id="anchor-multi-page1" class="anchor-target"></a>
        <div id="target-multi-page1" class="anchor-target"></div>
        
        <h1>Introduction</h1>
        <p>This is the introduction page. It uses multiple anchor techniques for maximum PDF compatibility.</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
    </div>

    <!-- PAGE 2 -->
    <div class="page" id="multi-page2">
        <!-- Multiple anchor techniques for maximum compatibility -->
        <a name="multi-page2"></a>
        <a id="anchor-multi-page2" class="anchor-target"></a>
        <div id="target-multi-page2" class="anchor-target"></div>
        
        <h1>Chapter 1: Getting Started</h1>
        <p>This is chapter 1 content. Testing multiple anchor techniques simultaneously.</p>
        <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.</p>
        <p>Totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt.</p>
        <p>Explicabo nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit.</p>
        <p>Sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
    </div>

    <!-- PAGE 3 -->
    <div class="page" id="multi-page3">
        <!-- Multiple anchor techniques for maximum compatibility -->
        <a name="multi-page3"></a>
        <a id="anchor-multi-page3" class="anchor-target"></a>
        <div id="target-multi-page3" class="anchor-target"></div>
        
        <h1>Chapter 2: Advanced Topics</h1>
        <p>This is chapter 2 content. Advanced topics with multiple anchor navigation.</p>
        <p>Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit.</p>
        <p>Sed quia non numquam eius modi tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem.</p>
        <p>Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam.</p>
        <p>Nisi ut aliquid ex ea commodi consequatur? Quis autem vel eum iure reprehenderit qui in ea voluptate.</p>
    </div>

    <!-- PAGE 4 -->
    <div class="page" id="multi-page4">
        <!-- Multiple anchor techniques for maximum compatibility -->
        <a name="multi-page4"></a>
        <a id="anchor-multi-page4" class="anchor-target"></a>
        <div id="target-multi-page4" class="anchor-target"></div>
        
        <h1>Conclusion</h1>
        <p>This is the conclusion page. Final thoughts with multiple anchor navigation techniques.</p>
        <p>At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium voluptatum.</p>
        <p>Deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate non provident.</p>
        <p>Similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum fuga.</p>
        <p>Et harum quidem rerum facilis est et expedita distinctio nam libero tempore.</p>
    </div>
</body>
</html>
