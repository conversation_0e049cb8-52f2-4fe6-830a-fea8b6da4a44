curl -X POST 'https://api.cloudflare.com/client/v4/accounts/cc1d2f7dc542ebba0fda8b084e50f938/browser-rendering/pdf' \
  -H 'Authorization: Bearer 0Fb27zAX6gHaFcsmuKGrou-XUrY8oPzGJUuFV39j' \
  -H 'Content-Type: application/json' \
  -d '{
    "url": "https://example.com/",
    "addStyleTag": [
      { "content": "body { font-family: Arial; }" },
      { "url": "https://cdn.jsdelivr.net/npm/bootstrap@3.3.7/dist/css/bootstrap.min.css" }
    ]
  }' \
  --output "output.pdf"


  curl "https://api.cloudflare.com/client/v4/accounts/cc1d2f7dc542ebba0fda8b084e50f938/tokens/verify" \
     -H "Authorization: Bearer 0Fb27zAX6gHaFcsmuKGrou-XUrY8oPzGJUuFV39j"