<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Letter Size Page with SVG Background</title>
    <link rel="stylesheet" href="https://api.fontshare.com/v2/css?f[]=clash-display@400,500,600&display=swap">
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            background-color: #f0f0f0;
        }

        .p6-letter-container {
            width: 21.59cm;
            height: 27.94cm;
            position: relative;
            background-color: white;
            box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .p6-background-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .p6-logo-box {
            position: absolute;
            width: 1.62cm;
            /* 46px converted to cm (46px / 28.35px per cm = 1.62cm) */
            height: 1.16cm;
            /* 33px converted to cm (33px / 28.35px per cm = 1.16cm) */
            bottom: 1.06cm;
            /* 30px converted to cm (30px / 28.35px per cm = 1.06cm) */
            right: 1.13cm;
            /* 32px converted to cm (32px / 28.35px per cm = 1.13cm) */
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2;
            overflow: hidden;
        }

        .p6-logo-box img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
        }


        .p6-company-logo-box {
            width: 4.3cm;
            /* 122px converted to cm (122px / 28.35px per cm = 4.3cm) */
            height: 1.09cm;
            /* 31px converted to cm (31px / 28.35px per cm = 1.09cm) */
            display: flex;
            justify-content: flex-start;
            align-items: center;
            overflow: hidden;
            object-fit: contain;
        }

        .p6-company-logo-box img {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            object-fit: contain;
        }

        .p6-fund-boxes-container {
            margin-top: 0.28cm;
            /* 8px converted to cm (8px / 28.35px per cm = 0.28cm) */
            display: flex;
            gap: 0.53cm;
            /* 15px converted to cm (15px / 28.35px per cm = 0.53cm) */
            justify-content: space-between;
            width: 100%;
        }

        .p6-fund-box {
            min-width: 1.5cm;
            /* width: 1.48cm; */
            /* 42px converted to cm (42px / 28.35px per cm = 1.48cm) */
            height: 0.6cm;
            /* 15px converted to cm (15px / 28.35px per cm = 0.53cm) */
            background: #B2D1B2;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Clash Display', sans-serif;
            font-size: 0.32cm;
            /* 9px converted to cm (9px / 28.35px per cm = 0.32cm) */
            padding-left: 0.2cm;
            padding-right: 0.2cm;
            font-weight: 500;
            flex-direction: row;
            white-space: nowrap;
        }

        .p6-fund-box .rank-value {
            font-weight: 700;
            margin-left: 0.14cm;
            /* 4px converted to cm (4px / 28.35px per cm = 0.14cm) */
        }

        .p6-fund-boxes-left {
            display: flex;
            gap: 0.53cm;
            /* 15px converted to cm (15px / 28.35px per cm = 0.53cm) */
        }

        .p6-website-text {
            color: #5CD468;
            font-size: 0.42cm;
            /* 12px converted to cm (12px / 28.35px per cm = 0.42cm) */
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            /* 18px converted to cm (18px / 28.35px per cm = 0.64cm) */
            word-wrap: break-word;
            align-self: center;
        }

        .p6-report-title {
            color: black;
            font-size: 0.42cm;
            /* 12px converted to cm (12px / 28.35px per cm = 0.42cm) */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            line-height: 0.64cm;
            /* 18px converted to cm (18px / 28.35px per cm = 0.64cm) */
            word-wrap: break-word;
            margin-top: 0.28cm;
            /* 8px converted to cm (8px / 28.35px per cm = 0.28cm) */
        }

        .p6-report-body {
            color: black;
            font-size: 0.42cm;
            /* 12px converted to cm (12px / 28.35px per cm = 0.42cm) */
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            /* 18px converted to cm (18px / 28.35px per cm = 0.64cm) */
            word-wrap: break-word;
            margin-top: 0.28cm;
            /* 8px converted to cm (8px / 28.35px per cm = 0.28cm) */
            /* margin-bottom: 0.5cm;  */
        }

        .p6-page-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.7cm;
            /* 20px converted to cm (20px / 28.35px per cm = 0.7cm) */
        }

        /* Portfolio Update Styles */
        .p6-portfolio-update-header {
            position: absolute;
            top: 3.39cm;
            /* 96px converted to cm (96px / 28.35px per cm = 3.39cm) */
            left: 2.19cm;
            /* 62px converted to cm (62px / 28.35px per cm = 2.19cm) */
            color: black;
            font-size: 0.53cm;
            /* 15px converted to cm (15px / 28.35px per cm = 0.53cm) */
            font-family: 'Clash Display', sans-serif;
            font-weight: 600;
            word-wrap: break-word;
            z-index: 2;
        }

        .p6-company-name-container {
            position: absolute;
            top: 4.33cm;
            /* 123px converted to cm (123px / 28.35px per cm = 4.33cm) */
            left: 2.19cm;
            /* 62px converted to cm (62px / 28.35px per cm = 2.19cm) */
            width: 10.58cm;
            /* 300px converted to cm (300px / 28.35px per cm = 10.58cm) */
            height: 1.06cm;
            /* 30px converted to cm (30px / 28.35px per cm = 1.06cm) */
            z-index: 2;
            overflow: hidden;
        }

        .p6-website-url {
            position: absolute;
            top: 4.93cm;
            /* 140px converted to cm (140px / 28.35px per cm = 4.93cm) */
            right: 2.19cm;
            /* 62px converted to cm (62px / 28.35px per cm = 2.19cm) */
            color: #5CD468;
            font-size: 0.42cm;
            /* 12px converted to cm (12px / 28.35px per cm = 0.42cm) */
            font-family: 'Clash Display', sans-serif;
            font-weight: 400;
            line-height: 0.64cm;
            /* 18px converted to cm (18px / 28.35px per cm = 0.64cm) */
            word-wrap: break-word;
            z-index: 2;
        }

        /* Updates Container */

        @media print {
            body {
                background-color: transparent;
                margin: 0;
                padding: 0;
            }

            .p6-letter-container {
                margin: 0;
                box-shadow: none;
                width: 21.59cm;
                height: 27.94cm;
            }

            @page {
                margin: 0;
                padding: 0;
                size: letter;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                color-adjust: exact;
            }

            .p6-page-container {
                gap: 0;
            }

            /* Force background images to print */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .p6-background-svg {
                display: block !important;
                visibility: visible !important;
            }
        }
    </style>
</head>

<body>
    <div class="p6-page-container" id="p6-pageContainer" style="display: flex; flex-direction: column; align-items: center; gap: 0.7cm;">
        <div class="p6-letter-container" style="width: 21.59cm; height: 27.94cm; position: relative; background-color: white; box-shadow: 0 0 0.35cm rgba(0, 0, 0, 0.1); overflow: hidden;">
            <svg width="612" height="792" viewBox="0 0 612 792" fill="none" xmlns="http://www.w3.org/2000/svg"
                class="p6-background-svg" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;">
                <g clip-path="url(#clip0_2020_2)">
                    <rect width="612" height="792" fill="white" />
                    <g opacity="0.5" filter="url(#filter0_f_2020_2)">
                        <ellipse cx="302.5" cy="894" rx="140.5" ry="132" fill="url(#paint0_linear_2020_2)" />
                    </g>
                </g>
                <defs>
                    <filter id="filter0_f_2020_2" x="-38" y="562" width="681" height="664" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                        <feGaussianBlur stdDeviation="100" result="effect1_foregroundBlur_2020_2" />
                    </filter>
                    <linearGradient id="paint0_linear_2020_2" x1="-534.922" y1="1245.17" x2="421.371" y2="44.5509"
                        gradientUnits="userSpaceOnUse">
                        <stop stop-color="#D1F15E" />
                        <stop offset="0.94" stop-color="#70E9EF" />
                    </linearGradient>
                    <clipPath id="clip0_2020_2">
                        <rect width="612" height="792" fill="white" />
                    </clipPath>
                </defs>
            </svg>

            <!-- Portfolio Update Content -->
            <div class="p6-portfolio-update-header" style="position: absolute; top: 3.39cm; left: 2.19cm; color: black; font-size: 0.53cm; font-family: 'Clash Display', sans-serif; font-weight: 600; word-wrap: break-word; z-index: 2;">Portfolio CompanyUpdates</div>


            <div class="update-container" style="position: absolute;
            top: 6.05cm; left: 2.19cm; right: 1.13cm; bottom: 3.2cm;
            z-index: 2; display: flex; flex-direction: column;
            gap: 0.1cm;">
                <!-- Updates content -->
                <div class="update-item" style="margin-bottom: 1cm;">
                    <div class="p6-company-logo-box" style="width: 4.3cm; height: 1.09cm; display: flex; justify-content: flex-start; align-items: center; overflow: hidden; object-fit: contain;">
                        <img src="MANIFEST.png" alt="comp Logo" style="max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain;">
                    </div>
                    <div class="p6-fund-boxes-container" style="margin-top: 0.28cm; display: flex; gap: 0.53cm; justify-content: space-between; width: 100%;">
                        <div class="p6-fund-boxes-left" style="display: flex; gap: 0.53cm;">
                            <div class="p6-fund-box" style="min-width: 1.5cm; height: 0.6cm; background: #B2D1B2; border-radius: 4px; display: flex; justify-content: center; align-items: center; font-family: 'Clash Display', sans-serif; font-size: 0.32cm; padding-left: 0.2cm; padding-right: 0.2cm; font-weight: 500; white-space: nowrap;">Fund 1</div>
                            <div class="p6-fund-box" style="min-width: 1.5cm; height: 0.6cm; background: #B2D1B2; border-radius: 4px; display: flex; justify-content: center; align-items: center; font-family: 'Clash Display', sans-serif; font-size: 0.32cm; padding-left: 0.2cm; padding-right: 0.2cm; font-weight: 500; white-space: nowrap;">Rank: <span class="rank-value" style="font-weight: 700; margin-left: 0.14cm;">A</span></div>
                        </div>
                        <div class="p6-website-text" style="color: #5CD468; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 400; line-height: 0.64cm; word-wrap: break-word; align-self: center;">manifestapp.xyz</div>
                    </div>
                    <div class="p6-report-container">
                        <div class="p6-report-title" style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 600; line-height: 0.64cm; word-wrap: break-word; margin-top: 0.28cm;">
                            Manifest
                        </div>
                        <div class="p6-report-body" style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 400; line-height: 0.64cm; word-wrap: break-word; margin-top: 0.28cm;">
                            Manifest has surpassed 110 million total manifestations to date, marking a 57%
                            quarter-over-quarter growth driven by strong TikTok virality and increased daily user
                            engagement through the introduction of new Manifest Challenges. The team recently unveiled
                            "Star," a brand mascot designed to strengthen emotional connection and boost user
                            engagement, taking cues from the success of mascots like Duolingo's Owl. Day 30 retention
                            remains robust at over 25%, supported by the implementation of user streaks that enhance app
                            stickiness. Additionally, Manifest expanded its product portfolio with the launch of a
                            second app, Daydream – Anxiety Meditations, tailored to the premium millennial audience
                            seeking wellness and mindfulness solutions.
                        </div>
                    </div>
                </div>

                <div class="update-item" style="margin-bottom: 1cm;">
                    <div class="p6-company-logo-box" style="width: 4.3cm; height: 1.09cm; display: flex; justify-content: flex-start; align-items: center; overflow: hidden; object-fit: contain;">
                        <img src="AUTONOMA.png" alt="comp Logo" style="max-width: 100%; max-height: 100%; width: auto; height: auto; object-fit: contain;">
                    </div>
                    <div class="p6-fund-boxes-container" style="margin-top: 0.28cm; display: flex; gap: 0.53cm; justify-content: space-between; width: 100%;">
                        <div class="p6-fund-boxes-left" style="display: flex; gap: 0.53cm;">
                            <div class="p6-fund-box" style="min-width: 1.5cm; height: 0.6cm; background: #B2D1B2; border-radius: 4px; display: flex; justify-content: center; align-items: center; font-family: 'Clash Display', sans-serif; font-size: 0.32cm; padding-left: 0.2cm; padding-right: 0.2cm; font-weight: 500; white-space: nowrap;">Angel Led dd</div>
                            <div class="p6-fund-box" style="min-width: 1.5cm; height: 0.6cm; background: #B2D1B2; border-radius: 4px; display: flex; justify-content: center; align-items: center; font-family: 'Clash Display', sans-serif; font-size: 0.32cm; padding-left: 0.2cm; padding-right: 0.2cm; font-weight: 500; white-space: nowrap;">Rank: <span class="rank-value" style="font-weight: 700; margin-left: 0.14cm;">B</span></div>
                        </div>
                        <div class="p6-website-text" style="color: #5CD468; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 400; line-height: 0.64cm; word-wrap: break-word; align-self: center;">manifestapp.xyz</div>
                    </div>
                    <div class="p6-report-container">
                        <div class="p6-report-title" style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 600; line-height: 0.64cm; word-wrap: break-word; margin-top: 0.28cm;">
                            Autonoma
                        </div>
                        <div class="p6-report-body" style="color: black; font-size: 0.42cm; font-family: 'Clash Display', sans-serif; font-weight: 400; line-height: 0.64cm; word-wrap: break-word; margin-top: 0.28cm;">
                            Autonoma closed Q1 with $340K in new ARR bookings, exceeding its $250K quota and nearly
                            doubling the $172K booked in Q1 2024. To accelerate growth, the company brought on a
                            marketing director who began building out Autonoma's marketing engine—generating
                            approximately 200 new leads within the first 60 days. The qualified opportunity pipeline has
                            grown significantly to $15M, up from just $2M in Q1 of the previous year, with several large
                            late-stage deals expected to close in Q2. Autonoma maintains a healthy burn rate with
                            approximately 16 months of runway and has reached ~$1M in ARR from software subscriptions,
                            up from $448K a year ago. Key highlights from the quarter include securing an airport
                            project with Cisco, a strong kickoff with Peachtree Corners, and joining Engage Cohort
                            14.
                        </div>
                    </div>
                </div>
            </div>

            <div class="p6-logo-box" style="position: absolute; width: 1.62cm; height: 1.16cm; bottom: 1.06cm; right: 1.13cm; display: flex; justify-content: center; align-items: center; z-index: 2;">
                <svg width="46" height="33" viewBox="0 0 46 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.00137067 20.9346V32.9975H8.10509V19.7046H1.22966C0.550474 19.7046 0 20.2552 0 20.9346H0.00137067Z" fill="#222222"/>
                    <path d="M17.3624 7.27033C18.0416 7.27033 18.592 6.71971 18.592 6.04034V0H3.49102H1.23161C0.552425 0 0.00195312 0.550616 0.00195312 1.22998V13.097H8.10568V7.27033H17.3624Z" fill="#222222"/>
                    <path d="M27.5053 20.9346V32.9975H35.609V19.7046H28.7336C28.0544 19.7046 27.5039 20.2552 27.5039 20.9346H27.5053Z" fill="#222222"/>
                    <path d="M19.0574 13.7188C19.2751 13.334 19.6846 13.097 20.1255 13.097H27.5062V1.22998C27.5062 0.550616 28.0567 0 28.7359 0H30.8994H46.0004V6.04034C46.0004 6.71971 45.45 7.27033 44.7708 7.27033H35.6099V13.097H27.513L19.4025 27.3774H25.0236V31.77C25.0236 32.4494 24.4731 33 23.7939 33H8.10547" fill="#222222"/>
                    <path d="M35.6113 19.7059H42.2512C43.1399 19.7059 43.8602 18.9855 43.8602 18.0965V13.0972H35.6113V19.7059Z" fill="#222222"/>
                    <path d="M8.08789 19.7059H11.1675C12.0562 19.7059 12.7765 18.9855 12.7765 18.0965V13.0972H8.08789V19.7059Z" fill="#222222"/>
                    </svg>
            </div>

            <div class="p6-page-number" style="position: absolute; bottom: 0.7cm; left: 1.7cm; color: black; font-size: 0.56cm; 
            font-family: 'Clash Display', sans-serif; font-weight: 700; line-height: 0.85cm; word-wrap: break-word; z-index: 2;"
            >1</div>
        </div>
    </div>
</body>

</html>