const ejs = require('ejs');
const fs = require('fs');
const path = require('path');

// Sample data for testing the template
const testData = {
    company_name: "TechFlow Solutions",
    company_logo_url: "https://via.placeholder.com/200x80/4A90E2/FFFFFF?text=TechFlow",
    website_url: "www.techflowsolutions.com",
    text_sections: [
        {
            title: "Company Overview",
            content: "TechFlow Solutions is a leading provider of cloud-based automation tools that help businesses streamline their operations and increase productivity. Founded in 2019, we've grown to serve over 500 enterprise clients worldwide."
        },
        {
            title: "Recent Achievements",
            content: "This quarter, we successfully launched our AI-powered analytics platform, secured partnerships with three Fortune 500 companies, and expanded our team by 40%. Our customer satisfaction score reached an all-time high of 4.8/5."
        },
        {
            title: "Market Position",
            content: "We maintain a strong competitive position in the enterprise automation space, with a 15% market share in our target segment. Our innovative approach and customer-centric focus continue to drive sustainable growth."
        },
        {
            title: "Future Outlook",
            content: "Looking ahead, we're focused on international expansion, with plans to enter the European market in Q2 2024. We're also investing heavily in R&D to maintain our technological edge and develop next-generation solutions."
        }
    ],
    key_metrics: [
        {
            label: "Annual Revenue",
            value: "$12.5M"
        },
        {
            label: "Revenue Growth",
            value: "+45% YoY"
        },
        {
            label: "Active Customers",
            value: "500+"
        },
        {
            label: "Customer Retention",
            value: "94%"
        },
        {
            label: "Team Size",
            value: "85 employees"
        },
        {
            label: "Market Share",
            value: "15%"
        }
    ]
};

// Function to render the template
async function renderTemplate() {
    try {
        // Read the EJS template
        const templatePath = path.join(__dirname, 'company-portfolio-with-metrics.ejs');
        const template = fs.readFileSync(templatePath, 'utf8');
        
        // Render the template with test data
        const html = ejs.render(template, testData);
        
        
        // Save the rendered HTML
        const outputPath = path.join(__dirname, 'test-output.html');
        fs.writeFileSync(outputPath, html);
        
        console.log('✅ Template rendered successfully!');
        console.log(`📄 Output saved to: ${outputPath}`);
        console.log('🌐 Open the HTML file in your browser to view the result');
        
        return outputPath;
        
    } catch (error) {
        console.error('❌ Error rendering template:', error.message);
        throw error;
    }
}

// Function to test with different data sets
function createAlternativeTestData() {
    return {
        company_name: "GreenTech Innovations",
        company_logo_url: "https://via.placeholder.com/200x80/2ECC71/FFFFFF?text=GreenTech",
        website_url: "www.greentech-innovations.com",
        text_sections: [
            {
                title: "Mission Statement",
                content: "GreenTech Innovations is dedicated to developing sustainable technology solutions that reduce environmental impact while driving business growth. We specialize in renewable energy systems and smart grid technologies."
            },
            {
                title: "Key Developments",
                content: "This year we launched our revolutionary solar panel efficiency optimizer, which increases energy output by 25%. We've also completed successful pilot programs with three major utility companies."
            },
            {
                title: "Environmental Impact",
                content: "Our solutions have helped clients reduce carbon emissions by over 50,000 tons annually. We're proud to contribute to a more sustainable future while delivering strong financial returns."
            }
        ],
        key_metrics: [
            {
                label: "Annual Revenue",
                value: "$8.2M"
            },
            {
                label: "Revenue Growth",
                value: "+62% YoY"
            },
            {
                label: "CO2 Reduction",
                value: "50K tons"
            },
            {
                label: "Patents Filed",
                value: "12"
            },
            {
                label: "Team Size",
                value: "45 employees"
            }
        ]
    };
}

// Main execution
async function main() {
    console.log('🚀 Starting template test...\n');
    
    try {
        // Test with primary data
        console.log('Testing with primary dataset...');
        await renderTemplate();
        
        // Test with alternative data
        console.log('\n📋 Alternative test data available. To test with different data:');
        console.log('1. Uncomment the lines below');
        console.log('2. Run the script again\n');
        
        /*
        // Uncomment these lines to test with alternative data
        console.log('Testing with alternative dataset...');
        const originalData = testData;
        Object.assign(testData, createAlternativeTestData());
        const altOutputPath = await renderTemplate();
        console.log(`Alternative output: ${altOutputPath.replace('test-output.html', 'test-output-alt.html')}`);
        */
        
    } catch (error) {
        console.error('Test failed:', error);
        process.exit(1);
    }
}

// Export for use in other files
module.exports = {
    renderTemplate,
    testData,
    createAlternativeTestData
};

// Run if this file is executed directly
if (require.main === module) {
    main();
} 